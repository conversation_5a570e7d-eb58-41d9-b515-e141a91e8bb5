from collections import defaultdict, deque

def topological_sort(graph):
    # graph 是 {節點: [相依節點, ...]} 例如 {'A': ['B'], 'B': ['C'], 'C': []}
    indegree = defaultdict(int)
    for u in graph:
        for v in graph[u]:
            indegree[v] += 1

    # 找出入度為 0 的節點（沒有其他檔案依賴它）
    queue = deque([u for u in graph if indegree[u] == 0])
    result = []
    while queue:
        u = queue.popleft()
        result.append(u)
        for v in graph[u]:
            indegree[v] -= 1
            if indegree[v] == 0:
                queue.append(v)
    return result

# 範例：A 依賴 B，B 依賴 C
graph = {
    'A.py': ['B.py', 'D.py'],
    'B.py': ['C.py'],
    'C.py': [],
    'D.py': ['B.py', 'E.py', 'F.py'],
    'E.py': ['F.py'],
    'F.py': []
}
print(topological_sort(graph))  # 可能輸出：['C.py', 'B.py', 'A.py']
