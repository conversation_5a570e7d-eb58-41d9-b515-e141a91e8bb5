#include <list>
#include <unordered_map>

class LRUCache {
    private:
        int capacity;
        std::list<std::pair<int, int>> cache;
        std::unordered_map<int, std::list<std::pair<int, int>>::iterator> map;
    public:
        LRUCache(int capacity) {
            this->capacity = capacity;
        }
        
        int get(int key) {
            if (map.find(key) == map.end()){
                return -1;
            }
            std::list<std::pair<int, int>>::iterator it = map[key];
            int value = it->second;
            cache.erase(it);
            cache.push_front({key, value});
            map[key] = cache.begin();
            return value;
        }
        
        void put(int key, int value) {
            if (map.find(key)!= map.end()){
                auto it = map[key];
                cache.erase(it);
                // erase
            } else if (cache.size() == capacity){
                auto lru = cache.back();
                map.erase(lru.first);
                cache.pop_back();
            }
            cache.push_front({key, value});
            map[key] = cache.begin();

        }
    };
    
    /**
     * Your LRUCache object will be instantiated and called as such:
     * LRUCache* obj = new LRUCache(capacity);
     * int param_1 = obj->get(key);
     * obj->put(key,value);
     */