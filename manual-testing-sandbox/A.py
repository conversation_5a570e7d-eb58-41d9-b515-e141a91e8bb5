import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

# Step 1: Prepare the data
data = {
    "month": ["2025/1", "2025/2", "2025/3", "2025/4"],
    "AI Chat LOC": [244703, 352123, 459928, 444605],
    "AI Edit LOC": [0, 0, 1405, 202175],
    "AI ICC LOC": [171910, 210247, 244153, 250280],
    "User LOC": [957456, 1074079, 1159481, 1748979],
    "AI LOC Ratio": [0.303196564364672, 0.343652628343444, 0.378283369089105, 0.339019946418023]
}

df = pd.DataFrame(data)
df['month'] = pd.to_datetime(df['month'])  # Convert month to datetime
df['month_num'] = df['month'].map(pd.Timestamp.toordinal)  # Numerical x-axis

# Step 2: Prepare plot data
x_vals = df['month_num'].to_numpy()
ai_chat = np.array(df["AI Chat LOC"], dtype=float)
ai_edit = np.array(df["AI Edit LOC"], dtype=float)
ai_icc = np.array(df["AI ICC LOC"], dtype=float)
user_loc = np.array(df["User LOC"], dtype=float)
ai_ratio = np.array(df["AI LOC Ratio"], dtype=float)

colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']
labels = ["AI Chat LOC", "AI Edit LOC", "AI ICC LOC", "User LOC"]

# Step 3: Create plot
fig, ax1 = plt.subplots(figsize=(12, 6))

# Plot overlapping area charts with border lines
for y_data, color, label in zip([ai_chat, ai_edit, ai_icc, user_loc], colors, labels):
    ax1.fill_between(x_vals, y_data, label=label, color=color, alpha=0.3)
    ax1.plot(x_vals, y_data, color=color, linewidth=2)

ax1.set_ylabel("Lines of Code (Absolute)")
ax1.set_xlabel("Month")
ax1.legend(loc="upper left")

# Format x-axis ticks as months
ax1.set_xticks(x_vals)
ax1.set_xticklabels([d.strftime('%Y-%m') for d in df['month']])

# Step 4: AI LOC Ratio line on right Y-axis
ax2 = ax1.twinx()
ax2.plot(x_vals, ai_ratio, color="blue", linewidth=2, marker='o', label="AI LOC Ratio")
ax2.set_ylabel("AI LOC Ratio", color="blue")
ax2.tick_params(axis='y', labelcolor="blue")
ax2.legend(loc="upper right")

# Add data labels to AI LOC Ratio points
for x, y in zip(x_vals, ai_ratio):
    ax2.text(x, y, f"{y:.2f}", color="blue", fontsize=10, ha='center', va='bottom')

plt.title("Overlapping AI/User LOC with AI LOC Ratio by Month")
plt.tight_layout()
plt.grid(True)
plt.show()
