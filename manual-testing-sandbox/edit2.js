const createBoundsTree = () => {
  return {
    rootNode: null,
    nodes: [],
    stack: [],

    insert(newBounds, data) {
      if (this.rootNode === null) {
        const newNode = this.pushLeaf(newBounds, data, 1);
        this.rootNode = newNode;
        return 1;
      }
      // TODO: implement insert logic for non-empty tree
      return -1;
    },

    pushLeaf(bounds, data, level) {
      const node = {
        bounds,
        data,
        level,
        children: []
      };
      this.nodes.push(node);
      return node;
    },

    clear() {
      this.rootNode = null;
      this.nodes = [];
      this.stack = [];
    },

    size() {
      return this.nodes.length;
    },

    isEmpty() {
      return this.rootNode === null;
    },

    search(queryBounds) {
      const results = [];
      if (!this.rootNode) return results;

      this.stack.push(this.rootNode);
      while (this.stack.length > 0) {
        const node = this.stack.pop();
        if (this.boundsOverlap(node.bounds, queryBounds)) {
          if (node.children.length === 0) {
            results.push(node.data);
          } else {
            this.stack.push(...node.children);
          }
        }
      }
      return results;
    },

    boundsOverlap(a, b) {
      // Example: 2D AABB check
      return (
        a.minX <= b.maxX &&
        a.maxX >= b.minX &&
        a.minY <= b.maxY &&
        a.maxY >= b.minY
      );
    }
  };
};
