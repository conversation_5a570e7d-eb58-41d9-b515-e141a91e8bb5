/**
 * Definition for singly-linked list.
 * struct ListNode {
 *     int val;
 *     ListNode *next;
 *     ListNode() : val(0), next(nullptr) {}
 *     ListNode(int x) : val(x), next(nullptr) {}
 *     ListNode(int x, ListNode *next) : val(x), next(next) {}
 * };
 */

 struct ListNode {
         int val;
         ListNode *next;
         ListNode() : val(0), next(nullptr) {}
         ListNode(int x) : val(x), next(nullptr) {}
         ListNode(int x, ListNode *next) : val(x), next(next) {}
    };
class Solution {
    public:
        ListNode* rotateRight(ListNode* head, int k) {
            if (!head || !head->next || k == 0) {
                return head; // Edge cases: empty list, single node, or no rotation
            }

            ListNode* curr;
            curr = head;
            int length = 1;
            while (curr->next !=nullptr){
                length++;
                curr = curr->next;
            }
            
            k = k % length;

            curr->next = head;

            // travers to the new tail
            curr = head;
            int stepsToNewHead = length-k;
            for (int i =0;i<stepsToNewHead - 1;i++){
                curr = curr->next;
            }

            ListNode* newHead = curr->next;
            curr->next = nullptr;
            return newHead;
        }
    };