#include <iostream>
#include <vector>
#include <stack>

struct TreeNode {
    int value;
    std::vector<TreeNode*> children;

    TreeNode(int val) : value(val) {}
};

std::vector<TreeNode*> dfsWithStack(TreeNode* root, int targetValue) {
    std::vector<TreeNode*> path; // To store the path from root to the target node
    if (!root) return path;

    std::stack<TreeNode*> nodeStack;
    std::stack<std::vector<TreeNode*>> pathStack;

    nodeStack.push(root);
    pathStack.push({root});

    while (!nodeStack.empty()) {
        TreeNode* currentNode = nodeStack.top();
        nodeStack.pop();

        path = pathStack.top();
        pathStack.pop();

        // Check if the current node matches the target value
        if (currentNode->value == targetValue) {
            return path; // Return the path to the target node
        }

        // Push children onto the stack
        for (TreeNode* child : currentNode->children) {
            nodeStack.push(child);

            // Create a new path including the child and push it onto the path stack
            std::vector<TreeNode*> newPath = path;
            newPath.push_back(child);
            pathStack.push(newPath);
        }
    }

    return {}; // Return an empty path if the target value is not found
}

int main() {
    // Example tree
    TreeNode* root = new TreeNode(1);
    TreeNode* child1 = new TreeNode(2);
    TreeNode* child2 = new TreeNode(3);
    TreeNode* child3 = new TreeNode(4);

    root->children = {child1, child2};
    child1->children = {child3};

    int targetValue = 4;
    std::vector<TreeNode*> path = dfsWithStack(root, targetValue);

    if (!path.empty()) {
        std::cout << "Path to target node: ";
        for (TreeNode* node : path) {
            std::cout << node->value << " ";
        }
        std::cout << std::endl;
    } else {
        std::cout << "Target node not found." << std::endl;
    }

    // Clean up memory
    delete root;
    delete child1;
    delete child2;
    delete child3;

    return 0;
}