import numpy as np

# 假設有3個候選片段，模型的可訓練參數為權重 (可理解為 retriever 的 logits)
retriever_weights = np.random.randn(3)
print("初始化權重:", retriever_weights)

def softmax(x):
    e_x = np.exp(x - np.max(x))
    return e_x / e_x.sum()

def simulate_perplexity(c):
    # 用隨機數模擬計算出 perplexity，真實情境應呼叫 LLM
    # 模型越「會選」越應該選到 perplexity 最小的那個
    true_perplexities = [2.0, 3.5, 1.2]  # 假設這是「oracle」的困惑度
    noise = np.random.randn() * 0.1
    return true_perplexities[c] + noise

def select_candidate(weights):
    probs = softmax(weights)
    candidate = np.random.choice(len(weights), p=probs)
    return candidate, probs

# RL訓練回合數
for epoch in range(200):
    # Step 1: 產生檢索分布並選一個候選
    candidate, probs = select_candidate(retriever_weights)
    
    # Step 2: 計算這個候選的 reward
    # 檢查這次選的是不是所有候選裡 perplexity 最低的（模擬 ground-truth）
    ppls = [simulate_perplexity(i) for i in range(len(retriever_weights))]
    best_cand = np.argmin(ppls)
    reward = 1 if candidate == best_cand else 0
    
    # Step 3: Policy gradient (REINFORCE) 更新 retriever_weights
    # log_prob = 選中這個候選的對數機率
    log_prob = np.log(probs[candidate] + 1e-8)
    # 損失函數 = -reward * log_prob (maximize expected reward)
    grad = -reward * (np.eye(len(retriever_weights))[candidate] - probs)
    # 這裡用 learning_rate = 0.1，實務上需tune
    retriever_weights -= 0.1 * grad

    if epoch % 20 == 0:
        print(f"Epoch {epoch}, weights={retriever_weights}, reward={reward}, ppls={np.round(ppls,2)}")

print("訓練後的retriever權重：", retriever_weights)
print("最終softmax分布：", softmax(retriever_weights))
