#include <iostream>
#include <string>

// Single-line comment to test comment handling
// Function declaration and definition
int add(int a, int b) {
    return a + b;
}

// Class definition
class MyClass {
public:
    MyClass(const std::string& name) : name(name) {}

    void greet() const {
        std::cout << "Hello, " << name << "!" << std::endl;
    }

private:
    std::string name;
};

// Function to test variable declaration and usage
void testVariables() {
    int x = 42; // Variable declaration
    int y = add(x, 8); // Function call
    std::cout << "Result: " << y << std::endl;
}

// Function to test object instantiation
void testObjectInstantiation() {
    MyClass obj("World"); // Object instantiation
    // obj.greet(); // Method call
}

// Main function
int main() {
    testVariables(); // Function call
    testObjectInstantiation(); // Function call
    
    
    return 0;
}